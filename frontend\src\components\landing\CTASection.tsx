import React from 'react';
import { <PERSON><PERSON><PERSON>, Spark<PERSON>, CheckCircle } from 'lucide-react';

export const CTASection: React.FC = () => {
  const features = [
    'Free 14-day trial',
    'No credit card required',
    'Full feature access',
    'Cancel anytime'
  ];

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-indigo-600/20 to-purple-600/20"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Badge */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium">
            <Sparkles className="h-4 w-4" />
            <span>Limited Time Offer</span>
          </div>
        </div>

        {/* Main headline */}
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
          Ready to Build the Future of AI?
        </h2>

        {/* Subheadline */}
        <p className="text-xl md:text-2xl text-indigo-100 mb-12 max-w-4xl mx-auto leading-relaxed">
          Join thousands of developers who are already building smarter AI applications 
          with EthosPrompt. Start your journey today.
        </p>

        {/* Features list */}
        <div className="flex flex-wrap justify-center gap-6 mb-12">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center space-x-2 text-white">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="font-medium">{feature}</span>
            </div>
          ))}
        </div>

        {/* CTA buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <button className="inline-flex items-center px-8 py-4 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200 shadow-lg">
            Start Free Trial
            <ArrowRight className="ml-2 h-5 w-5" />
          </button>
          <button className="inline-flex items-center px-8 py-4 bg-transparent text-white font-semibold rounded-lg border-2 border-white/30 hover:border-white/50 hover:bg-white/10 transition-all duration-200">
            Schedule Demo
          </button>
        </div>

        {/* Trust indicators */}
        <div className="text-center">
          <p className="text-indigo-200 text-sm mb-4">
            Trusted by teams at
          </p>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {/* Company logos placeholder */}
            {['TechCorp', 'StartupXYZ', 'Enterprise Inc', 'AI Labs', 'DevTeam'].map((company, index) => (
              <div key={index} className="text-white font-semibold text-lg">
                {company}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
