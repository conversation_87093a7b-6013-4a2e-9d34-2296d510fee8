import React from 'react';
import { CheckCircle, ArrowRight, Clock, DollarSign } from 'lucide-react';

export const BenefitsSection: React.FC = () => {
  const benefits = [
    {
      title: 'Reduce Development Time',
      description: 'Cut AI development time by 70% with pre-built, tested prompt templates.',
      metric: '70%',
      metricLabel: 'Time Saved'
    },
    {
      title: 'Improve AI Accuracy',
      description: 'Get more relevant results with context-aware prompts and RAG integration.',
      metric: '3x',
      metricLabel: 'Better Results'
    },
    {
      title: 'Scale Effortlessly',
      description: 'From prototype to production, our platform grows with your needs.',
      metric: '∞',
      metricLabel: 'Scalability'
    }
  ];

  const features = [
    'Version-controlled prompt management',
    'Real-time collaboration tools',
    'Advanced analytics and monitoring',
    'Multi-LLM provider support',
    'Enterprise security and compliance',
    'Custom workflow automation',
    'API-first architecture',
    '24/7 support and documentation'
  ];

  return (
    <section className="py-20 lg:py-32 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Benefits cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          {benefits.map((benefit, index) => (
            <div key={index} className="text-center">
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="text-4xl font-bold text-indigo-600 mb-2">
                  {benefit.metric}
                </div>
                <div className="text-sm text-gray-500 mb-4">
                  {benefit.metricLabel}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {benefit.title}
                </h3>
                <p className="text-gray-600">
                  {benefit.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Main content */}
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Why Choose EthosPrompt?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Stop reinventing the wheel. Our platform provides everything you need 
              to build production-ready AI applications faster and more reliably.
            </p>
            
            <div className="space-y-4 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            <button className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200">
              Learn More
              <ArrowRight className="ml-2 h-4 w-4" />
            </button>
          </div>

          <div className="relative">
            {/* Placeholder for dashboard/product screenshot */}
            <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-600 h-12 flex items-center px-6">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-white/30 rounded-full"></div>
                  <div className="w-3 h-3 bg-white/30 rounded-full"></div>
                  <div className="w-3 h-3 bg-white/30 rounded-full"></div>
                </div>
              </div>
              <div className="p-8">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg h-80 flex items-center justify-center">
                  <div className="text-gray-500 text-lg font-medium">
                    Product Dashboard Preview
                  </div>
                </div>
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
              <Clock className="inline h-4 w-4 mr-1" />
              Real-time
            </div>
            <div className="absolute -bottom-4 -left-4 bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
              <DollarSign className="inline h-4 w-4 mr-1" />
              Cost-effective
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
