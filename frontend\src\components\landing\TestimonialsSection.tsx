import React from 'react';
import { Star, Quote } from 'lucide-react';

export const TestimonialsSection: React.FC = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'AI Engineer',
      company: 'TechCorp',
      avatar: 'SC',
      content: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> transformed how we build AI applications. The RAG integration is seamless and the prompt library saved us months of development time.',
      rating: 5
    },
    {
      name: '<PERSON>',
      role: 'CTO',
      company: 'StartupXYZ',
      avatar: 'MR',
      content: 'The workflow automation features are incredible. We went from manual prompt engineering to fully automated AI pipelines in just weeks.',
      rating: 5
    },
    {
      name: '<PERSON>',
      role: 'Product Manager',
      company: 'Enterprise Inc',
      avatar: 'EW',
      content: 'Finally, a platform that makes AI accessible to our entire team. The collaboration features and analytics give us the insights we need.',
      rating: 5
    }
  ];

  const stats = [
    { value: '500+', label: 'Active Users' },
    { value: '10k+', label: 'Prompts Created' },
    { value: '99.9%', label: 'Uptime' },
    { value: '4.9/5', label: 'User Rating' }
  ];

  return (
    <section className="py-20 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Trusted by AI Teams Worldwide
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join thousands of developers and teams who are building better AI with EthosPrompt.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-indigo-600 mb-2">
                {stat.value}
              </div>
              <div className="text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              {/* Quote icon */}
              <Quote className="h-8 w-8 text-indigo-600 mb-4" />
              
              {/* Rating */}
              <div className="flex space-x-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Content */}
              <p className="text-gray-700 mb-6 leading-relaxed">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center">
                <div className="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                  {testimonial.avatar}
                </div>
                <div>
                  <div className="font-semibold text-gray-900">
                    {testimonial.name}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {testimonial.role} at {testimonial.company}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Transform Your AI Development?
            </h3>
            <p className="text-gray-600 mb-6">
              Join the community of developers building the future of AI applications.
            </p>
            <button className="inline-flex items-center px-8 py-4 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200 shadow-lg">
              Start Building Today
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};
