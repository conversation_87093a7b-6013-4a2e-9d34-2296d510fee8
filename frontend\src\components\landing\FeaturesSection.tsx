import React from 'react';
import { 
  Brain, 
  Database, 
  Zap, 
  Target, 
  Users, 
  Code,
  FileText,
  TrendingUp 
} from 'lucide-react';

const features = [
  {
    icon: Brain,
    title: 'AI-Powered Prompts',
    description: 'Intelligent prompt templates that adapt to your data and context for better results.',
    color: 'text-indigo-600'
  },
  {
    icon: Database,
    title: 'RAG Integration',
    description: 'Connect your prompts to live data sources for accurate, up-to-date responses.',
    color: 'text-purple-600'
  },
  {
    icon: Zap,
    title: 'Workflow Automation',
    description: 'Chain prompts together to create complex, multi-step AI workflows.',
    color: 'text-yellow-600'
  },
  {
    icon: Target,
    title: 'Precision Targeting',
    description: 'Fine-tune prompts for specific use cases and domains with advanced parameters.',
    color: 'text-green-600'
  },
  {
    icon: Users,
    title: 'Team Collaboration',
    description: 'Share prompts, collaborate on workflows, and manage team access controls.',
    color: 'text-blue-600'
  },
  {
    icon: Code,
    title: 'Developer-Friendly',
    description: 'Full API access, version control, and integration with your existing tools.',
    color: 'text-red-600'
  }
];

export const FeaturesSection: React.FC = () => {
  return (
    <section className="py-20 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Everything you need to build
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
              {" "}smarter AI
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From simple prompts to complex workflows, our platform provides all the tools 
            you need to create intelligent, data-aware AI systems.
          </p>
        </div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="group p-8 bg-white rounded-2xl border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-300"
            >
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-lg bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300`}>
                  <feature.icon className={`h-6 w-6 ${feature.color}`} />
                </div>
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-2 text-sm text-gray-500 mb-4">
            <FileText className="h-4 w-4" />
            <span>Comprehensive documentation and examples included</span>
          </div>
          <div className="inline-flex items-center space-x-2 text-sm text-gray-500">
            <TrendingUp className="h-4 w-4" />
            <span>Trusted by 500+ developers and growing</span>
          </div>
        </div>
      </div>
    </section>
  );
};
